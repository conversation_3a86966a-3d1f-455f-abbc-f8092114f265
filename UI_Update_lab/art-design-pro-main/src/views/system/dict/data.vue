<template>
  <div class="dict-data-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:dict:add'" @click="handleAdd" v-ripple> 新增数据 </ElButton>
          <ElButton
            v-auth="'system:dict:edit'"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'system:dict:remove'"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'system:dict:export'" @click="handleExport" v-ripple> 导出 </ElButton>
          <ElButton @click="handleClose" v-ripple> 关闭 </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="dictCode"
        :loading="loading"
        :columns="columns"
        :data="dictDataList"
        :stripe="true"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      />

      <!-- 使用ArtTable内置分页，无需单独的分页组件 -->

      <!-- 添加或修改字典数据对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="500px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
          <ElFormItem label="字典类型">
            <ElInput v-model="form.dictType" :disabled="true" />
          </ElFormItem>
          <ElFormItem label="数据标签" prop="dictLabel">
            <ElInput v-model="form.dictLabel" placeholder="请输入数据标签" />
          </ElFormItem>
          <ElFormItem label="数据键值" prop="dictValue">
            <ElInput v-model="form.dictValue" placeholder="请输入数据键值" />
          </ElFormItem>
          <ElFormItem label="样式属性" prop="cssClass">
            <ElInput v-model="form.cssClass" placeholder="请输入样式属性" />
          </ElFormItem>
          <ElFormItem label="显示排序" prop="dictSort">
            <ElInputNumber
              v-model="form.dictSort"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </ElFormItem>
          <ElFormItem label="回显样式" prop="listClass">
            <ElSelect v-model="form.listClass" style="width: 100%">
              <ElOption
                v-for="item in listClassOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="状态" prop="status">
            <ElRadioGroup v-model="form.status">
              <ElRadio value="0">正常</ElRadio>
              <ElRadio value="1">停用</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="3" />
          </ElFormItem>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, ref, reactive, computed, onMounted, h } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTableColumns } from '@/composables/useTableColumns'
  import { useAuth } from '@/composables/useAuth'
  import { useWorktabStore } from '@/store/modules/worktab'
  import { DictApi } from '@/api/system/dict'
  import type { DictData, DictDataQueryParams, DictType } from '@/types/system/dict'
  import type { FormInstance, FormRules } from 'element-plus'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'

  defineOptions({ name: 'SystemDictData' })

  const { hasAuth } = useAuth()
  const route = useRoute()
  const router = useRouter()
  const worktabStore = useWorktabStore()

  const loading = ref(false)
  const dialogVisible = ref(false)
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    dictType: '',
    dictLabel: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 字典数据
  const dictDataList = ref<DictData[]>([])
  const total = ref(0)
  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)
  const defaultDictType = ref('')
  const typeOptions = ref<DictType[]>([])

  // 分页配置
  const pagination = reactive({
    current: 1,
    size: 10,
    total: 0
  })

  // 查询参数
  const queryParams = reactive<DictDataQueryParams>({
    pageNum: 1,
    pageSize: 10,
    dictType: '',
    dictLabel: '',
    status: ''
  })

  // 表单数据
  const form = reactive<DictData>({
    dictCode: undefined,
    dictLabel: '',
    dictValue: '',
    dictType: '',
    cssClass: '',
    listClass: 'default',
    dictSort: 0,
    status: '0',
    remark: ''
  })

  // 数据标签回显样式选项
  const listClassOptions = ref([
    { value: 'default', label: '默认' },
    { value: 'primary', label: '主要' },
    { value: 'success', label: '成功' },
    { value: 'info', label: '信息' },
    { value: 'warning', label: '警告' },
    { value: 'danger', label: '危险' }
  ])

  // 表单验证规则
  const rules = reactive<FormRules>({
    dictLabel: [{ required: true, message: '数据标签不能为空', trigger: 'blur' }],
    dictValue: [{ required: true, message: '数据键值不能为空', trigger: 'blur' }],
    dictSort: [{ required: true, message: '数据顺序不能为空', trigger: 'blur' }]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改字典数据' : '新增字典数据'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      dictType: defaultDictType.value,
      dictLabel: '',
      status: ''
    })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(queryParams, {
      pageNum: 1,
      ...formFilters
    })
    getTableData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '字典名称',
      key: 'dictType',
      type: 'select',
      props: { clearable: true },
      options: typeOptions.value.map((item) => ({
        label: item.dictName,
        value: item.dictType
      }))
    },
    {
      label: '字典标签',
      key: 'dictLabel',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ]
    }
  ])

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      type: 'selection',
      width: 55
    },
    {
      prop: 'dictCode',
      label: '字典编码',
      width: 100
    },
    {
      prop: 'dictLabel',
      label: '字典标签',
      minWidth: 120,
      formatter: (row: DictData) => {
        if (
          (row.listClass === '' || row.listClass === 'default') &&
          (row.cssClass === '' || row.cssClass === null)
        ) {
          return h('span', row.dictLabel)
        } else {
          return h(
            ElTag,
            {
              type: row.listClass === 'primary' ? '' : (row.listClass as any),
              class: row.cssClass
            },
            () => row.dictLabel
          )
        }
      }
    },
    {
      prop: 'dictValue',
      label: '字典键值',
      minWidth: 120
    },
    {
      prop: 'dictSort',
      label: '字典排序',
      width: 100
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      formatter: (row: DictData) => {
        return h(ElTag, { type: row.status === '0' ? 'success' : 'danger' }, () =>
          row.status === '0' ? '正常' : '停用'
        )
      }
    },
    {
      prop: 'remark',
      label: '备注',
      minWidth: 120,
      formatter: (row: DictData) => row.remark || '--'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      formatter: (row: DictData) => row.createTime || '--'
    },
    {
      prop: 'operation',
      label: '操作',
      width: 150,
      formatter: (row: DictData) => {
        return h('div', [
          hasAuth('system:dict:edit') &&
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleUpdate(row)
            }),
          hasAuth('system:dict:remove') &&
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
        ])
      }
    }
  ])

  onMounted(() => {
    // 获取字典类型详情
    const dictId = route.params?.dictId as string
    if (dictId) {
      getDictTypeDetail(parseInt(dictId))
    }
    getDictTypeOptions()
  })

  // 查询字典类型详细信息
  const getDictTypeDetail = async (dictId: number) => {
    try {
      const response = await DictApi.getDictTypeDetail(dictId)
      const dictTypeData = (response.data as any)?.data || response.data
      queryParams.dictType = dictTypeData.dictType
      defaultDictType.value = dictTypeData.dictType
      getTableData()
    } catch (error) {
      console.error('获取字典类型详情失败:', error)
      ElMessage.error('获取字典类型详情失败')
    }
  }

  // 获取字典类型选项
  const getDictTypeOptions = async () => {
    try {
      const response = await DictApi.getDictTypeOptions()
      typeOptions.value = (response.data as any)?.data || response.data || []
    } catch (error) {
      console.error('获取字典类型选项失败:', error)
    }
  }

  const getTableData = async () => {
    loading.value = true
    try {
      const response = await DictApi.getDictDataList(queryParams)
      // 从嵌套的RuoYi响应格式中提取数据：response.data.rows
      dictDataList.value = (response.data as any)?.rows || response.rows || []
      total.value = (response.data as any)?.total || response.total || 0
      pagination.total = total.value
      pagination.current = queryParams.pageNum || 1
      pagination.size = queryParams.pageSize || 10
    } catch (error) {
      console.error('获取字典数据列表失败:', error)
      ElMessage.error('获取字典数据列表失败')
    } finally {
      loading.value = false
    }
  }

  // 分页大小变化处理
  const handleSizeChange = (size: number) => {
    queryParams.pageSize = size
    queryParams.pageNum = 1
    pagination.size = size
    pagination.current = 1
    getTableData()
  }

  // 当前页变化处理
  const handleCurrentChange = (current: number) => {
    queryParams.pageNum = current
    pagination.current = current
    getTableData()
  }

  const handleRefresh = () => {
    console.log('字典数据管理刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      dictType: defaultDictType.value,
      dictLabel: '',
      status: ''
    })
    // 重置分页
    pagination.current = 1
    pagination.size = 10
    getTableData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: DictData[]) => {
    ids.value = selection.map((item) => item.dictCode!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 新增按钮操作
  const handleAdd = () => {
    resetForm()
    form.dictType = queryParams.dictType
    dialogVisible.value = true
    isEdit.value = false
  }

  // 修改按钮操作
  const handleUpdate = async (row?: DictData) => {
    resetForm()
    const dictCode = row?.dictCode || ids.value[0]

    try {
      const response = await DictApi.getDictDataDetail(dictCode)
      const dictData = (response.data as any)?.data || response.data
      Object.assign(form, dictData)
      dialogVisible.value = true
      isEdit.value = true
    } catch (error) {
      console.error('获取字典数据信息失败:', error)
      ElMessage.error('获取字典数据信息失败')
    }
  }

  // 删除按钮操作
  const handleDelete = async (row?: DictData) => {
    const dictCodes = row?.dictCode ? [row.dictCode] : ids.value

    try {
      await ElMessageBox.confirm(
        `是否确认删除字典编码为"${dictCodes.join(',')}"的数据项？`,
        '系统提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await DictApi.deleteDictData(dictCodes.length === 1 ? dictCodes[0] : dictCodes)
      ElMessage.success('删除成功')
      getTableData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 导出按钮操作
  const handleExport = async () => {
    try {
      const response = await DictApi.exportDictData(queryParams)
      const { handleExportResponse } = await import('@/utils/download')
      handleExportResponse(response, `dict_data_${new Date().getTime()}.xlsx`)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  // 关闭按钮操作
  const handleClose = async () => {
    try {
      // 保存当前页面路径，避免响应式更新影响
      const currentPath = route.path
      const dictManagementPath = '/system/dict'

      // 先跳转到字典管理页面
      await router.push(dictManagementPath)

      // 延迟关闭当前标签，确保路由跳转完成
      setTimeout(() => {
        // 使用保存的路径关闭标签
        worktabStore.removeTab(currentPath)
      }, 100)
    } catch (error) {
      console.error('关闭字典数据页面失败:', error)
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (form.dictCode) {
            await DictApi.updateDictData(form)
            ElMessage.success('修改成功')
          } else {
            await DictApi.addDictData(form)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          getTableData()
        } catch {
          ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
        }
      }
    })
  }

  // 重置表单
  const resetForm = () => {
    // 逐个重置响应式属性，确保响应式更新
    form.dictCode = undefined
    form.dictLabel = ''
    form.dictValue = ''
    form.dictType = queryParams.dictType
    form.cssClass = ''
    form.listClass = 'default'
    form.dictSort = 0
    form.status = '0'
    form.remark = ''

    // 重置表单验证状态
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }
</script>

<style lang="scss" scoped>
  .dict-data-page {
    .dialog-footer {
      text-align: right;
    }

    .art-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
