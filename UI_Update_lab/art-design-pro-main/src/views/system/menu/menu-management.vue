<template>
  <div class="menu-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton type="primary" @click="handleAdd" v-ripple>
            <template #icon>
              <i class="iconfont-sys">&#xe6d5;</i>
            </template>
            新增菜单
          </ElButton>
          <ElButton @click="toggleExpand" v-ripple>
            <template #icon>
              <i class="iconfont-sys">&#xe724;</i>
            </template>
            {{ isExpanded ? '收起' : '展开' }}
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="menuId"
        :loading="loading"
        :columns="columns"
        :data="menuList"
        :stripe="false"
        :default-expand-all="isExpanded"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      />

      <!-- RuoYi风格的菜单对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="680px" append-to-body>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="100px">
          <ElRow>
            <!-- 上级菜单 -->
            <ElCol :span="24">
              <ElFormItem label="上级菜单">
                <ElTreeSelect
                  v-model="form.parentId"
                  :data="menuOptions"
                  :props="{ value: 'menuId', label: 'menuName', children: 'children' }"
                  value-key="menuId"
                  placeholder="选择上级菜单"
                  check-strictly
                />
              </ElFormItem>
            </ElCol>
            
            <!-- 菜单类型 -->
            <ElCol :span="24">
              <ElFormItem label="菜单类型" prop="menuType">
                <ElRadioGroup v-model="form.menuType">
                  <ElRadio label="M">目录</ElRadio>
                  <ElRadio label="C">菜单</ElRadio>
                  <ElRadio label="F">按钮</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            
            <!-- 菜单图标 -->
            <ElCol :span="12" v-if="form.menuType !== 'F'">
              <ElFormItem label="菜单图标" prop="icon">
                <ElPopover placement="bottom-start" :width="540" trigger="click">
                  <template #reference>
                    <ElInput v-model="form.icon" placeholder="点击选择图标" readonly>
                      <template #prefix>
                        <i
                          v-if="form.icon"
                          class="iconfont-sys"
                          v-html="form.icon"
                          style="height: 32px;width: 16px;"
                        ></i>
                        <i v-else class="iconfont-sys" style="height: 32px;width: 16px;">&#xe6f7;</i>
                      </template>
                    </ElInput>
                  </template>
                  <ArtIconSelector
                    ref="iconSelectRef"
                    v-model="form.icon"
                    :iconType="iconType"
                    width="100%"
                  />
                </ElPopover>
              </ElFormItem>
            </ElCol>
            
            <!-- 显示排序 -->
            <ElCol :span="12">
              <ElFormItem label="显示排序" prop="orderNum">
                <ElInputNumber
                  v-model="form.orderNum"
                  controls-position="right"
                  :min="0"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
            
            <!-- 菜单名称 -->
            <ElCol :span="12">
              <ElFormItem label="菜单名称" prop="menuName">
                <ElInput v-model="form.menuName" placeholder="请输入菜单名称" />
              </ElFormItem>
            </ElCol>
            
            <!-- 路由名称 -->
            <ElCol :span="12" v-if="form.menuType === 'C'">
              <ElFormItem prop="routeName">
                <template #label>
                  <span>
                    <ElTooltip content="默认不填则和路由地址相同" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    路由名称
                  </span>
                </template>
                <ElInput v-model="form.routeName" placeholder="请输入路由名称" />
              </ElFormItem>
            </ElCol>
            
            <!-- 是否外链 -->
            <ElCol :span="12" v-if="form.menuType !== 'F'">
              <ElFormItem>
                <template #label>
                  <span>
                    <ElTooltip content="选择是外链则路由地址需要以http(s)://开头" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    是否外链
                  </span>
                </template>
                <ElRadioGroup v-model="form.isFrame">
                  <ElRadio label="0">是</ElRadio>
                  <ElRadio label="1">否</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            
            <!-- 路由地址 -->
            <ElCol :span="12" v-if="form.menuType !== 'F'">
              <ElFormItem prop="path">
                <template #label>
                  <span>
                    <ElTooltip content="访问的路由地址，如：user" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    路由地址
                  </span>
                </template>
                <ElInput v-model="form.path" placeholder="请输入路由地址" />
              </ElFormItem>
            </ElCol>
            
            <!-- 组件路径 -->
            <ElCol :span="12" v-if="form.menuType === 'C'">
              <ElFormItem prop="component">
                <template #label>
                  <span>
                    <ElTooltip content="访问的组件路径，如：system/user/index" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    组件路径
                  </span>
                </template>
                <ElInput v-model="form.component" placeholder="请输入组件路径" />
              </ElFormItem>
            </ElCol>
            
            <!-- 权限字符 -->
            <ElCol :span="12" v-if="form.menuType !== 'M'">
              <ElFormItem>
                <template #label>
                  <span>
                    <ElTooltip content="控制器中定义的权限字符" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    权限字符
                  </span>
                </template>
                <ElInput v-model="form.perms" placeholder="请输入权限标识" maxlength="100" />
              </ElFormItem>
            </ElCol>
            
            <!-- 路由参数 -->
            <ElCol :span="12" v-if="form.menuType === 'C'">
              <ElFormItem>
                <template #label>
                  <span>
                    <ElTooltip content='访问路由的默认传递参数，如：{"id": 1, "name": "ry"}' placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    路由参数
                  </span>
                </template>
                <ElInput v-model="form.query" placeholder="请输入路由参数" maxlength="255" />
              </ElFormItem>
            </ElCol>
            
            <!-- 是否缓存 -->
            <ElCol :span="12" v-if="form.menuType === 'C'">
              <ElFormItem>
                <template #label>
                  <span>
                    <ElTooltip content="选择是则会被keep-alive缓存" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    是否缓存
                  </span>
                </template>
                <ElRadioGroup v-model="form.isCache">
                  <ElRadio label="0">缓存</ElRadio>
                  <ElRadio label="1">不缓存</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            
            <!-- 显示状态 -->
            <ElCol :span="12" v-if="form.menuType !== 'F'">
              <ElFormItem>
                <template #label>
                  <span>
                    <ElTooltip content="选择隐藏则路由将不会出现在侧边栏" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    显示状态
                  </span>
                </template>
                <ElRadioGroup v-model="form.visible">
                  <ElRadio label="0">显示</ElRadio>
                  <ElRadio label="1">隐藏</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            
            <!-- 菜单状态 -->
            <ElCol :span="12">
              <ElFormItem>
                <template #label>
                  <span>
                    <ElTooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                      <ElIcon><QuestionFilled /></ElIcon>
                    </ElTooltip>
                    菜单状态
                  </span>
                </template>
                <ElRadioGroup v-model="form.status">
                  <ElRadio label="0">正常</ElRadio>
                  <ElRadio label="1">停用</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        
        <template #footer>
          <div class="dialog-footer">
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
            <ElButton @click="cancel">取 消</ElButton>
          </div>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage, ElMessageBox, ElTag, ElTreeSelect, ElTooltip, ElIcon, ElPopover } from 'element-plus'
  import { QuestionFilled } from '@element-plus/icons-vue'
  import { IconTypeEnum } from '@/enums/appEnum'
  import { useTableColumns } from '@/composables/useTableColumns'
  import type { Menu, MenuForm, MenuQueryParams } from '@/types/system/menu'
  import { listMenu, getMenu, addMenu, updateMenu, delMenu } from '@/api/system/menu'
  import { parseTime, handleTree } from '@/utils/ruoyi'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import ArtIconSelector from '@/components/core/base/art-icon-selector/index.vue'

  defineOptions({ name: 'MenuManagement' })

  const loading = ref(true)
  const menuList = ref<Menu[]>([])
  const menuOptions = ref<Menu[]>([])
  const isExpanded = ref(false)
  const tableRef = ref()
  
  // 对话框相关
  const dialogVisible = ref(false)
  const dialogTitle = ref('')
  const isEdit = ref(false)
  const formRef = ref<FormInstance>()
  const iconSelectRef = ref()
  const iconType = ref(IconTypeEnum.UNICODE)

  // 定义表单搜索初始值
  const initialSearchState = {
    menuName: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 增加实际应用的搜索条件状态
  const appliedFilters = reactive({ ...initialSearchState })
  
  // 菜单表单数据
  const form = reactive<MenuForm>({
    menuId: undefined,
    parentId: 0,
    menuName: '',
    orderNum: 0,
    path: '',
    component: '',
    query: '',
    routeName: '',
    isFrame: '1',
    isCache: '0',
    menuType: 'M',
    visible: '0',
    status: '0',
    perms: '',
    icon: '',
    remark: ''
  })

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '菜单名称',
      key: 'menuName',
      type: 'input',
      props: {
        clearable: true,
        placeholder: '请输入菜单名称'
      }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: {
        clearable: true,
        placeholder: '菜单状态'
      },
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ]
    }
  ])
  
  // 表单验证规则
  const rules = reactive<FormRules>({
    menuName: [{ required: true, message: '菜单名称不能为空', trigger: 'blur' }],
    orderNum: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }],
    path: [{ required: true, message: '路由地址不能为空', trigger: 'blur' }]
  })

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      prop: 'menuName',
      label: '菜单名称',
      minWidth: 160,
      align: 'left'
    },
    {
      prop: 'icon',
      label: '图标',
      width: 100,
      align: 'center',
      formatter: (row: Menu) => {
        return row.icon ? h('i', { class: 'iconfont-sys', innerHTML: row.icon }) : ''
      }
    },
    {
      prop: 'orderNum',
      label: '排序',
      width: 60,
      align: 'center'
    },
    {
      prop: 'perms',
      label: '权限标识',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'component',
      label: '组件路径',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'status',
      label: '状态',
      width: 80,
      align: 'center',
      formatter: (row: Menu) => {
        return h(ElTag, { type: row.status === '0' ? 'success' : 'danger' }, () =>
          row.status === '0' ? '正常' : '停用'
        )
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      align: 'center',
      formatter: (row: Menu) => {
        return parseTime(row.createTime)
      }
    },
    {
      prop: 'operation',
      label: '操作',
      width: 210,
      align: 'center',
      formatter: (row: Menu) => {
        return h('div', { class: 'flex gap-2' }, [
          h(ArtButtonTable, {
            type: 'edit',
            onClick: () => handleUpdate(row)
          }),
          h(ArtButtonTable, {
            type: 'add',
            onClick: () => handleAdd(row)
          }),
          h(ArtButtonTable, {
            type: 'delete',
            onClick: () => handleDelete(row)
          })
        ])
      }
    }
  ])

  const handleRefresh = () => {
    getList()
  }

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    getList()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(appliedFilters, { ...formFilters })
    getList()
  }

  /** 查询菜单列表 */
  const getList = () => {
    loading.value = true
    const queryParams: MenuQueryParams = {
      menuName: appliedFilters.menuName || undefined,
      status: appliedFilters.status || undefined
    }
    
    listMenu(queryParams)
      .then((response: any) => {
        menuList.value = handleTree(response.data, 'menuId')
      })
      .finally(() => {
        loading.value = false
      })
  }

  /** 查询菜单下拉树结构 */
  const getTreeselect = () => {
    menuOptions.value = []
    listMenu().then((response: any) => {
      const menu = { menuId: 0, menuName: '主类目', children: [] }
      menu.children = handleTree(response.data, 'menuId')
      menuOptions.value.push(menu)
    })
  }

  /** 取消按钮 */
  const cancel = () => {
    dialogVisible.value = false
    reset()
  }

  /** 表单重置 */
  const reset = () => {
    Object.assign(form, {
      menuId: undefined,
      parentId: 0,
      menuName: '',
      icon: '',
      menuType: 'M',
      orderNum: 0,
      isFrame: '1',
      isCache: '0',
      visible: '0',
      status: '0',
      path: '',
      component: '',
      query: '',
      routeName: '',
      perms: '',
      remark: ''
    })
    formRef.value?.resetFields()
  }

  /** 新增按钮操作 */
  const handleAdd = (row?: Menu) => {
    reset()
    getTreeselect()
    if (row && row.menuId) {
      form.parentId = row.menuId
    } else {
      form.parentId = 0
    }
    dialogVisible.value = true
    dialogTitle.value = '添加菜单'
    isEdit.value = false
  }

  /** 展开/折叠操作 */
  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
    nextTick(() => {
      if (tableRef.value && menuList.value) {
        const processRows = (rows: Menu[]) => {
          rows.forEach((row) => {
            if (row.children && row.children.length > 0) {
              tableRef.value.elTableRef.toggleRowExpansion(row, isExpanded.value)
              processRows(row.children)
            }
          })
        }
        processRows(menuList.value)
      }
    })
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row: Menu) => {
    reset()
    await getTreeselect()
    if (row.menuId) {
      getMenu(row.menuId).then((response: any) => {
        Object.assign(form, response.data)
        dialogVisible.value = true
        dialogTitle.value = '修改菜单'
        isEdit.value = true
      })
    }
  }

  /** 提交按钮 */
  const submitForm = () => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        if (form.menuId) {
          updateMenu(form).then(() => {
            ElMessage.success('修改成功')
            dialogVisible.value = false
            getList()
          })
        } else {
          addMenu(form).then(() => {
            ElMessage.success('新增成功')
            dialogVisible.value = false
            getList()
          })
        }
      }
    })
  }

  /** 删除按钮操作 */
  const handleDelete = (row: Menu) => {
    ElMessageBox.confirm(`是否确认删除名称为"${row.menuName}"的数据项?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (row.menuId) {
        return delMenu(row.menuId)
      }
    }).then(() => {
      getList()
      ElMessage.success('删除成功')
    }).catch(() => {})
  }

  // 选择图标
  const selected = (name: string) => {
    form.icon = name
  }

  // 组件挂载时获取数据
  onMounted(() => {
    getList()
  })

  const tableData = ref<Menu[]>([])
</script>

<style lang="scss" scoped>
  .menu-page {
    .iconfont-sys {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
    
    .flex {
      display: flex;
    }
    
    .gap-2 {
      gap: 0.5rem;
    }

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }
  }
</style>