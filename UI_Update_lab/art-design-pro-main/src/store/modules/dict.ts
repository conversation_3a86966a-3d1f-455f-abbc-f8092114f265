import { defineStore } from 'pinia'
import type { DictOption } from '@/types/system/dict'

/**
 * 字典数据Store
 * 保持与RuoYi一致的API接口和数据格式
 */
export const useDictStore = defineStore('dict', {
  state: () => ({
    /** 字典数据缓存 */
    dict: new Array<{ key: string; value: DictOption[] }>()
  }),

  actions: {
    /**
     * 获取字典数据
     * @param key 字典类型
     * @returns 字典选项数组或null
     */
    getDict(key: string): DictOption[] | null {
      if (key == null || key == '') {
        return null
      }
      try {
        for (let i = 0; i < this.dict.length; i++) {
          if (this.dict[i].key == key) {
            return this.dict[i].value
          }
        }
        return null
      } catch (e) {
        return null
      }
    },

    /**
     * 设置字典数据
     * @param key 字典类型
     * @param value 字典选项数组
     */
    setDict(key: string, value: DictOption[]): void {
      if (key !== null && key !== '') {
        // 检查是否已存在，如果存在则更新，否则添加
        const existingIndex = this.dict.findIndex((item) => item.key === key)
        if (existingIndex !== -1) {
          this.dict[existingIndex].value = value
        } else {
          this.dict.push({
            key: key,
            value: value
          })
        }
      }
    },

    /**
     * 删除字典数据
     * @param key 字典类型
     * @returns 是否删除成功
     */
    removeDict(key: string): boolean {
      try {
        for (let i = 0; i < this.dict.length; i++) {
          if (this.dict[i].key == key) {
            this.dict.splice(i, 1)
            return true
          }
        }
        return false
      } catch (e) {
        return false
      }
    },

    /**
     * 清空所有字典数据
     */
    cleanDict(): void {
      this.dict = []
    },

    /**
     * 初始化字典（预留接口）
     */
    initDict(): void {
      // 预留接口，可用于系统启动时预加载常用字典
    }
  }
})

export default useDictStore
