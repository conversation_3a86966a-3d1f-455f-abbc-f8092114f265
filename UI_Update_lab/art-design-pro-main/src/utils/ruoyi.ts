/**
 * RuoYi工具函数
 * 提供parseTime、handleTree等RuoYi风格的工具函数
 */

/**
 * 时间格式化
 * @param time 时间
 * @param pattern 格式模式
 * @returns 格式化后的时间字符串
 */
export function parseTime(time?: string | number | Date, pattern = '{y}-{m}-{d} {h}:{i}:{s}'): string {
  if (!time) {
    return ''
  }

  let date: Date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // 如果是纯数字字符串，视为时间戳
        time = parseInt(time)
      } else {
        // 替换为可解析的格式
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }

  const formatObj: Record<string, number> = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }

  const timeStr = pattern.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })

  return timeStr
}

/**
 * 构造树型结构数据
 * @param data 数据源
 * @param id id字段 默认 'id'
 * @param parentId 父节点字段 默认 'parentId'
 * @param children 孩子节点字段 默认 'children'
 * @returns 树形结构数据
 */
export function handleTree<T extends Record<string, any>>(
  data: T[],
  id = 'id',
  parentId = 'parentId',
  children = 'children'
): T[] {
  if (!Array.isArray(data) || data.length === 0) {
    return []
  }

  // 用于存储所有节点，以ID为键
  const nodeMap: Record<string | number, T> = {}
  
  // 根节点数组
  const rootNodes: T[] = []
  
  // 首先，将所有节点存入map中，并初始化children数组
  data.forEach(item => {
    const nodeId = item[id]
    nodeMap[nodeId] = { ...item }
    nodeMap[nodeId][children] = []
  })
  
  // 然后，遍历所有节点，构建父子关系
  data.forEach(item => {
    const nodeId = item[id]
    const parentNodeId = item[parentId]
    const currentNode = nodeMap[nodeId]
    
    // 如果没有父节点或父节点ID为0，则为根节点
    if (!parentNodeId || parentNodeId === 0 || !nodeMap[parentNodeId]) {
      rootNodes.push(currentNode)
    } else {
      // 将当前节点添加到父节点的children中
      const parentNode = nodeMap[parentNodeId]
      if (parentNode) {
        parentNode[children].push(currentNode)
      }
    }
  })
  
  return rootNodes
}

/**
 * 日期格式化
 * @param date 日期
 * @param fmt 格式
 * @returns 格式化后的日期字符串
 */
export function dateFormat(date: Date | string | number, fmt = 'yyyy-MM-dd hh:mm:ss'): string {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
  
  const o: Record<string, number> = {
    'M+': dateObj.getMonth() + 1, // 月份
    'd+': dateObj.getDate(), // 日
    'h+': dateObj.getHours(), // 小时
    'm+': dateObj.getMinutes(), // 分
    's+': dateObj.getSeconds(), // 秒
    'q+': Math.floor((dateObj.getMonth() + 3) / 3), // 季度
    S: dateObj.getMilliseconds() // 毫秒
  }
  
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (dateObj.getFullYear() + '').substring(4 - RegExp.$1.length))
  }
  
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k].toString() : ('00' + o[k]).substring(('' + o[k]).length)
      )
    }
  }
  
  return fmt
}

/**
 * 添加日期范围
 * @param params 参数对象
 * @param dateRange 日期范围数组
 * @param propName 属性名前缀
 * @returns 添加了日期范围的参数对象
 */
export function addDateRange<T extends Record<string, any>>(
  params: T,
  dateRange: [string, string] | null,
  propName = 'Time'
): T {
  if (!dateRange || dateRange.length !== 2) {
    return params
  }
  
  const search = { ...params }
  search['begin' + propName] = dateRange[0]
  search['end' + propName] = dateRange[1]
  
  return search
}

/**
 * 回显数据字典
 * @param datas 数据字典数组
 * @param value 值
 * @returns 字典标签
 */
export function selectDictLabel(datas: Array<{ label: string; value: string }>, value: string): string {
  if (!value || !Array.isArray(datas)) return ''
  
  const dict = datas.find(item => item.value === value)
  return dict ? dict.label : value
}

/**
 * 回显数据字典（多个值）
 * @param datas 数据字典数组
 * @param value 值（逗号分隔）
 * @param separator 分隔符
 * @returns 字典标签（逗号分隔）
 */
export function selectDictLabels(
  datas: Array<{ label: string; value: string }>,
  value: string,
  separator = ','
): string {
  if (!value || !Array.isArray(datas)) return ''
  
  const values = value.split(separator)
  const labels = values.map(val => {
    const dict = datas.find(item => item.value === val.trim())
    return dict ? dict.label : val
  })
  
  return labels.join(separator)
}

/**
 * 下载文件
 * @param data 文件数据
 * @param fileName 文件名
 * @param mimeType MIME类型
 */
export function download(data: BlobPart, fileName: string, mimeType = 'application/octet-stream'): void {
  const blob = new Blob([data], { type: mimeType })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 字符串格式化(%s )
 * @param str 原字符串
 * @param args 参数
 * @returns 格式化后的字符串
 */
export function sprintf(str: string, ...args: any[]): string {
  let flag = true
  let i = 0
  
  str = str.replace(/%s/g, () => {
    const arg = args[i++]
    if (typeof arg === 'undefined') {
      flag = false
      return ''
    }
    return arg
  })
  
  return flag ? str : ''
}

/**
 * 转换字符串，undefined、null等转化为空字符串
 * @param str 字符串
 * @returns 转换后的字符串
 */
export function parseStrEmpty(str: any): string {
  if (!str || str === 'undefined' || str === 'null') {
    return ''
  }
  return str
}