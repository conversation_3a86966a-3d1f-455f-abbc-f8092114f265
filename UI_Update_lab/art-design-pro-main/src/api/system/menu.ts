/**
 * 菜单管理API
 * 基于RuoYi接口格式，保持数据结构与RuoYi一致
 */

import http from '@/utils/http'
import type { Menu, MenuQueryParams, MenuTreeOption, RoleMenuTreeSelectResponse } from '@/types/system/menu'

/**
 * 查询菜单列表
 * @param query 查询参数
 * @returns 菜单列表
 */
export function listMenu(query?: MenuQueryParams) {
  return http.get<Menu[]>('/system/menu/list', { params: query })
}

/**
 * 查询菜单详细信息
 * @param menuId 菜单ID
 * @returns 菜单详细信息
 */
export function getMenu(menuId: number) {
  return http.get<Menu>(`/system/menu/${menuId}`)
}

/**
 * 查询菜单下拉树结构
 * @returns 菜单树选择项列表
 */
export function treeselect() {
  return http.get<MenuTreeOption[]>('/system/menu/treeselect')
}

/**
 * 根据角色ID查询菜单下拉树结构
 * @param roleId 角色ID
 * @returns 角色菜单树选择响应
 */
export function roleMenuTreeselect(roleId: number) {
  return http.get<RoleMenuTreeSelectResponse>(`/system/menu/roleMenuTreeselect/${roleId}`)
}

/**
 * 新增菜单
 * @param data 菜单信息
 * @returns 操作结果
 */
export function addMenu(data: Menu) {
  return http.post('/system/menu', data)
}

/**
 * 修改菜单
 * @param data 菜单信息
 * @returns 操作结果
 */
export function updateMenu(data: Menu) {
  return http.put('/system/menu', data)
}

/**
 * 删除菜单
 * @param menuId 菜单ID
 * @returns 操作结果
 */
export function delMenu(menuId: number) {
  return http.delete(`/system/menu/${menuId}`)
}

/**
 * 获取路由列表（用于动态路由生成）
 * @returns 路由配置列表
 */
export function getRouters() {
  return http.get('/getRouters')
}