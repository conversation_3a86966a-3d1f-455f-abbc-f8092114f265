# 菜单系统备份说明

## 备份时间
2025-08-27

## 备份原因
在实现RuoYi风格的菜单管理模块之前，对现有的art-design-pro菜单系统进行备份，确保迁移过程中不会丢失原有功能。

## 备份文件列表

### 1. 菜单管理页面
- **原路径**: `src/views/system/menu/index.vue`
- **备份路径**: `backup/menu-system/views/system/menu/index.vue`
- **说明**: 现有的菜单管理页面，包含菜单列表、搜索、编辑等功能

### 2. 菜单API服务
- **原路径**: `src/api/menuApi.ts`
- **备份路径**: `backup/menu-system/api/menuApi.ts`
- **说明**: 菜单相关的API接口，包含路由获取和转换逻辑

### 3. 菜单类型定义
- **原路径**: `src/types/system/menu.ts`
- **备份路径**: `backup/menu-system/types/system/menu.ts`
- **说明**: 菜单相关的TypeScript类型定义

### 4. 菜单状态管理
- **原路径**: `src/store/modules/menu.ts`
- **备份路径**: `backup/menu-system/store/modules/menu.ts`
- **说明**: 菜单的Pinia状态管理模块

### 5. 菜单路由转换工具
- **原路径**: `src/router/utils/menuToRouter.ts`
- **备份路径**: `backup/menu-system/router/utils/menuToRouter.ts`
- **说明**: 菜单数据到路由配置的转换工具

## 保留的文件
以下文件将保留不变，因为它们是框架的核心菜单组件：
- `src/components/core/layouts/art-menus/` - 菜单显示组件
- `src/router/guards/beforeEach.ts` - 路由守卫（菜单处理逻辑）
- `src/router/routes/staticRoutes.ts` - 静态路由配置

## 迁移计划
1. 重构菜单管理页面，实现RuoYi风格的菜单管理功能
2. 扩展菜单API，支持RuoYi的菜单CRUD操作
3. 更新菜单类型定义，确保与RuoYi数据格式兼容
4. 保持现有的菜单显示和路由功能不变

## 恢复方法
如果需要恢复原有的菜单系统，可以将备份文件复制回原位置：
```bash
cp backup/menu-system/views/system/menu/index.vue src/views/system/menu/
cp backup/menu-system/api/menuApi.ts src/api/
cp backup/menu-system/types/system/menu.ts src/types/system/
cp backup/menu-system/store/modules/menu.ts src/store/modules/
cp backup/menu-system/router/utils/menuToRouter.ts src/router/utils/
```

## 注意事项
- 备份仅包含菜单管理相关的文件，不包括菜单显示组件
- 迁移过程中会保持现有的菜单显示功能不变
- 新的菜单管理功能将基于RuoYi数据格式实现
