import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { LanguageEnum } from '@/enums/appEnum'
import { router } from '@/router'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { AppRouteRecord } from '@/types/router'
import { setPageTitle } from '@/router/utils/utils'
import { resetRouterState } from '@/router/guards/beforeEach'
import { RoutesAlias } from '@/router/routesAlias'
import { useMenuStore } from './menu'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { LoginApi } from '@/api/auth/login'
import type { UserInfo } from '@/types/system/auth'

/**
 * 用户状态管理
 * 直接存储RuoYi数据格式，不做任何转换
 * 管理用户登录状态、个人信息、权限、角色等状态的管理和持久化
 */
export const useUserStore = defineStore(
  'userStore',
  () => {
    // 访问令牌（从Cookie中初始化）
    const token = ref(getToken() || '')
    // 用户基本信息（直接存储RuoYi格式）
    const userInfo = ref<UserInfo>({} as UserInfo)
    // 角色列表（直接存储RuoYi格式）
    const roles = ref<string[]>([])
    // 权限列表（直接存储RuoYi格式，如 'system:user:add'）
    const permissions = ref<string[]>([])

    // 保留原有的UI状态管理
    // 语言设置
    const language = ref(LanguageEnum.ZH)
    // 锁屏状态
    const isLock = ref(false)
    // 锁屏密码
    const lockPassword = ref('')
    // 搜索历史记录
    const searchHistory = ref<AppRouteRecord[]>([])
    // 刷新令牌
    const refreshToken = ref('')

    // 计算属性：登录状态（基于token判断）
    const isLogin = computed(() => !!token.value)
    // 计算属性：获取用户信息
    const getUserInfo = computed(() => userInfo.value)
    // 计算属性：获取用户名
    const userName = computed(() => userInfo.value?.userName || '')
    // 计算属性：获取用户ID
    const userId = computed(() => userInfo.value?.userId)
    // 计算属性：获取设置状态
    const getSettingState = computed(() => useSettingStore().$state)
    // 计算属性：获取工作台状态
    const getWorktabState = computed(() => useWorktabStore().$state)
    // 计算属性：获取用户权限列表
    const getPermissions = computed(() => permissions.value)
    // 计算属性：获取用户角色列表
    const getRoles = computed(() => roles.value)

    /**
     * 设置用户信息（直接存储RuoYi格式）
     * @param newInfo 新的用户信息
     */
    const setUserInfo = (newInfo: UserInfo) => {
      userInfo.value = newInfo
    }

    /**
     * 设置用户权限（直接存储RuoYi权限格式）
     * @param newPermissions 权限数组
     */
    const setPermissions = (newPermissions: string[]) => {
      permissions.value = newPermissions
    }

    /**
     * 设置用户角色（直接存储RuoYi角色格式）
     * @param newRoles 角色数组
     */
    const setRoles = (newRoles: string[]) => {
      roles.value = newRoles
    }

    /**
     * 设置令牌
     * @param newToken 访问令牌
     */
    const setTokenValue = (newToken: string) => {
      token.value = newToken
      setToken(newToken)
    }

    /**
     * 获取用户信息（从服务器获取）
     */
    const getUserInfoFromServer = async (): Promise<void> => {
      try {
        const response = await LoginApi.getUserInfo()
        // 直接使用RuoYi响应数据，不做任何转换
        userInfo.value = response.user
        roles.value = response.roles
        permissions.value = response.permissions
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    }

    /**
     * 设置语言
     * @param lang 语言枚举值
     */
    const setLanguage = (lang: LanguageEnum) => {
      setPageTitle(router.currentRoute.value)
      language.value = lang
    }

    /**
     * 设置搜索历史
     * @param list 搜索历史列表
     */
    const setSearchHistory = (list: AppRouteRecord[]) => {
      searchHistory.value = list
    }

    /**
     * 设置锁屏状态
     * @param status 锁屏状态
     */
    const setLockStatus = (status: boolean) => {
      isLock.value = status
    }

    /**
     * 设置锁屏密码
     * @param password 锁屏密码
     */
    const setLockPassword = (password: string) => {
      lockPassword.value = password
    }

    /**
     * 退出登录
     * 清空所有用户相关状态并跳转到登录页
     */
    const logOut = async (): Promise<void> => {
      try {
        // 调用退出登录API
        await LoginApi.logout()
      } catch (error) {
        console.error('退出登录失败:', error)
      } finally {
        // 清空用户信息
        userInfo.value = {} as UserInfo
        // 清空角色和权限
        roles.value = []
        permissions.value = []
        // 重置锁屏状态
        isLock.value = false
        // 清空锁屏密码
        lockPassword.value = ''
        // 清空访问令牌
        token.value = ''
        // 清空刷新令牌
        refreshToken.value = ''
        // 移除Cookie中的token
        removeToken()
        // 清空工作台已打开页面
        useWorktabStore().opened = []
        // 移除iframe路由缓存
        sessionStorage.removeItem('iframeRoutes')
        // 清空主页路径
        useMenuStore().setHomePath('')
        // 重置路由状态
        resetRouterState()
        // 跳转到登录页
        router.push(RoutesAlias.Login)
      }
    }

    return {
      // 状态
      token,
      userInfo,
      roles,
      permissions,
      language,
      isLock,
      lockPassword,
      searchHistory,
      refreshToken,

      // 计算属性
      isLogin,
      getUserInfo,
      userName,
      userId,
      getSettingState,
      getWorktabState,
      getPermissions,
      getRoles,

      // 方法
      setUserInfo,
      setPermissions,
      setRoles,
      setTokenValue,
      getUserInfoFromServer,
      setLanguage,
      setSearchHistory,
      setLockStatus,
      setLockPassword,
      logOut
    }
  },
  {
    persist: {
      key: 'user-store',
      storage: localStorage
    }
  }
)
