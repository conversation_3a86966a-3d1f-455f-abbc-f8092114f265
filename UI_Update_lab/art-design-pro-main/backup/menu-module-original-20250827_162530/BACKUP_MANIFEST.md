# Art Design Pro 菜单模块备份清单

## 备份信息
- 备份时间: 2025-08-27 16:25:30
- 备份原因: 准备从RuoYi框架迁移菜单模块，避免代码冲突
- 备份范围: 所有与菜单管理相关的现有代码

## 备份文件清单

### 1. 视图组件 (views)
- `views-menu/` - 菜单管理页面组件
- `views-nested/` - 嵌套菜单示例组件

### 2. API接口 (api)
- `api/menuApi.ts` - 菜单相关API接口

### 3. 类型定义 (types)
- `types/menu.ts` - 菜单相关TypeScript类型定义
- `types/system.ts` - 系统相关类型定义

### 4. 状态管理 (store)
- `store/menu.ts` - 菜单状态管理store
- `store/user.ts` - 用户状态管理store（包含菜单相关状态）
- `store/setting.ts` - 设置状态管理store（包含菜单主题等）

### 5. 路由工具 (router-utils)
- `router-utils/menuToRouter.ts` - 菜单转路由的工具函数

### 6. 布局组件 (components)
- `components/art-menus/` - 菜单相关的布局组件
- `components/art-settings-panel/` - 设置面板组件

## 注意事项
1. 这些文件是art-design-pro-main框架原有的菜单系统
2. 在迁移RuoYi菜单模块时，请谨慎处理，避免覆盖重要功能
3. 建议在迁移完成后，对比新旧代码，确保功能完整性
4. 如需恢复，可以从此备份目录恢复相关文件

## 推荐迁移策略
1. 优先保留art-design-pro-main的UI组件和布局系统
2. 迁移RuoYi的业务逻辑和数据结构
3. 整合两套系统的优点，形成统一的菜单管理方案

