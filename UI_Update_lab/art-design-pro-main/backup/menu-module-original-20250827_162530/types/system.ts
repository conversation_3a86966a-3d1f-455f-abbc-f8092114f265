/**
 * Store状态类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { UserInfo, Menu, DictData, RouterConfig } from '@/types/system'

/** 用户状态 */
export interface UserState {
  /** 访问令牌 */
  token: string
  /** 用户信息 */
  userInfo: UserInfo | null
  /** 角色列表 */
  roles: string[]
  /** 权限列表 */
  permissions: string[]
}

/** 权限状态 */
export interface PermissionState {
  /** 路由列表 */
  routes: RouterConfig[]
  /** 动态路由 */
  addRoutes: RouterConfig[]
  /** 默认路由 */
  defaultRoutes: RouterConfig[]
  /** 顶部菜单 */
  topbarRouters: RouterConfig[]
  /** 侧边栏菜单 */
  sidebarRouters: RouterConfig[]
}

/** 字典状态 */
export interface DictState {
  /** 字典数据 */
  dict: Record<string, DictData[]>
}

/** 设置状态 */
export interface SettingsState {
  /** 主题 */
  theme: string
  /** 侧边栏Logo */
  sidebarLogo: boolean
  /** 是否显示设置的右面板 */
  showSettings: boolean
  /** 是否显示顶部导航 */
  topNav: boolean
  /** 是否显示 tagsView */
  tagsView: boolean
  /** 是否固定头部 */
  fixedHeader: boolean
  /** 是否显示侧边栏Logo */
  showSidebarLogo: boolean
  /** 动态标题 */
  dynamicTitle: boolean
  /** 侧边栏文字主题 */
  sideTheme: string
  /** 主题颜色 */
  themeColor: string
}

/** 应用状态 */
export interface AppState {
  /** 侧边栏状态 */
  sidebar: {
    /** 是否打开 */
    opened: boolean
    /** 是否没有动画 */
    withoutAnimation: boolean
    /** 是否隐藏 */
    hide: boolean
  }
  /** 设备类型 */
  device: 'desktop' | 'mobile'
  /** 尺寸 */
  size: string
}

/** 标签视图状态 */
export interface TagsViewState {
  /** 访问的视图 */
  visitedViews: TagView[]
  /** 缓存的视图 */
  cachedViews: string[]
  /** 是否刷新 */
  iframeViews: TagView[]
}

/** 标签视图 */
export interface TagView {
  /** 路径 */
  path: string
  /** 名称 */
  name?: string
  /** 标题 */
  title?: string
  /** 查询参数 */
  query?: Record<string, any>
  /** 参数 */
  params?: Record<string, any>
  /** 是否固定 */
  affix?: boolean
  /** 完整路径 */
  fullPath?: string
  /** 元信息 */
  meta?: {
    /** 标题 */
    title?: string
    /** 图标 */
    icon?: string
    /** 是否不缓存 */
    noCache?: boolean
    /** 链接 */
    link?: string
  }
}

/** 根状态 */
export interface RootState {
  /** 用户模块 */
  user: UserState
  /** 权限模块 */
  permission: PermissionState
  /** 字典模块 */
  dict: DictState
  /** 设置模块 */
  settings: SettingsState
  /** 应用模块 */
  app: AppState
  /** 标签视图模块 */
  tagsView: TagsViewState
}

/** 用户模块Actions */
export interface UserActions {
  /** 登录 */
  login: (loginForm: any) => Promise<void>
  /** 获取用户信息 */
  getInfo: () => Promise<void>
  /** 退出登录 */
  logOut: () => Promise<void>
  /** 前端登出 */
  fedLogOut: () => Promise<void>
}

/** 权限模块Actions */
export interface PermissionActions {
  /** 生成路由 */
  generateRoutes: (roles: string[]) => Promise<RouterConfig[]>
  /** 设置默认路由 */
  setDefaultRoutes: (routes: RouterConfig[]) => void
  /** 设置顶部路由 */
  setTopbarRoutes: (routes: RouterConfig[]) => void
  /** 设置侧边栏路由 */
  setSidebarRouters: (routes: RouterConfig[]) => void
}

/** 字典模块Actions */
export interface DictActions {
  /** 获取字典 */
  getDict: (dictType: string) => Promise<DictData[]>
  /** 设置字典 */
  setDict: (dictType: string, dictData: DictData[]) => void
  /** 删除字典 */
  removeDict: (dictType: string) => void
  /** 清空字典 */
  cleanDict: () => void
}

/** 设置模块Actions */
export interface SettingsActions {
  /** 修改设置 */
  changeSetting: (data: { key: string; value: any }) => void
}

/** 应用模块Actions */
export interface AppActions {
  /** 切换侧边栏 */
  toggleSideBar: (withoutAnimation?: boolean) => void
  /** 关闭侧边栏 */
  closeSideBar: (withoutAnimation: boolean) => void
  /** 切换设备 */
  toggleDevice: (device: 'desktop' | 'mobile') => void
  /** 设置尺寸 */
  setSize: (size: string) => void
  /** 设置语言 */
  setLanguage: (language: string) => void
}

/** 标签视图模块Actions */
export interface TagsViewActions {
  /** 添加视图 */
  addView: (view: TagView) => void
  /** 添加访问的视图 */
  addVisitedView: (view: TagView) => void
  /** 删除视图 */
  delView: (view: TagView) => Promise<{ visitedViews: TagView[]; cachedViews: string[] }>
  /** 删除访问的视图 */
  delVisitedView: (view: TagView) => Promise<TagView[]>
  /** 删除缓存的视图 */
  delCachedView: (view: TagView) => Promise<string[]>
  /** 删除其他视图 */
  delOthersViews: (view: TagView) => Promise<{ visitedViews: TagView[]; cachedViews: string[] }>
  /** 删除所有视图 */
  delAllViews: () => Promise<{ visitedViews: TagView[]; cachedViews: string[] }>
  /** 更新访问的视图 */
  updateVisitedView: (view: TagView) => void
}
