/**
 * 菜单相关类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 菜单信息 */
export interface Menu {
  /** 菜单ID */
  menuId?: number
  /** 菜单名称 */
  menuName: string
  /** 父菜单ID */
  parentId?: number
  /** 显示顺序 */
  orderNum?: number
  /** 路由地址 */
  path?: string
  /** 组件路径 */
  component?: string
  /** 路由参数 */
  query?: string
  /** 是否为外链（0是 1否） */
  isFrame?: string
  /** 是否缓存（0缓存 1不缓存） */
  isCache?: string
  /** 菜单类型（M目录 C菜单 F按钮） */
  menuType?: string
  /** 菜单状态（0显示 1隐藏） */
  visible?: string
  /** 菜单状态（0正常 1停用） */
  status?: string
  /** 权限标识 */
  perms?: string
  /** 菜单图标 */
  icon?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 子菜单 */
  children?: Menu[]
}

/** 菜单查询参数 */
export interface MenuQueryParams {
  /** 菜单名称 */
  menuName?: string
  /** 状态（0显示 1隐藏） */
  visible?: string
}

/** 路由配置信息 */
export interface RouterConfig {
  /** 路由名字 */
  name?: string
  /** 路由地址 */
  path: string
  /** 是否隐藏路由，当设置 true 的时候该路由不会再侧边栏出现 */
  hidden?: boolean
  /** 重定向地址，当设置 noRedirect 的时候该路由在面包屑导航中不可被点击 */
  redirect?: string
  /** 组件地址 */
  component?: string
  /** 路由参数：如 {"id": 1, "name": "ry"} */
  query?: string
  /** 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面 */
  alwaysShow?: boolean
  /** 其他元素 */
  meta?: RouterMeta
  /** 子路由 */
  children?: RouterConfig[]
}

/** 路由元信息 */
export interface RouterMeta {
  /** 设置该路由在侧边栏和面包屑中展示的名字 */
  title: string
  /** 设置该路由的图标，对应路径src/assets/icons/svg */
  icon?: string
  /** 设置为true，则不会被 <keep-alive>缓存 */
  noCache?: boolean
  /** 内链地址（http(s)://开头） */
  link?: string
}

/** 菜单树选择项 */
export interface MenuTreeOption {
  /** 菜单ID */
  id: number
  /** 菜单名称 */
  label: string
  /** 子菜单 */
  children?: MenuTreeOption[]
}

/** 角色菜单树选择响应 */
export interface RoleMenuTreeSelectResponse {
  /** 菜单树信息 */
  menus: MenuTreeOption[]
  /** 选中菜单列表 */
  checkedKeys: number[]
}
