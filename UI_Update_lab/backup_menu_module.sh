#!/bin/bash

# 菜单模块备份脚本
# 备份art-design-pro-main中现有的菜单模块相关代码
# 执行时间: $(date '+%Y-%m-%d %H:%M:%S')

echo "开始备份art-design-pro-main菜单模块..."

# 创建备份目录
BACKUP_DIR="art-design-pro-main/backup/menu-module-original-$(date '+%Y%m%d_%H%M%S')"
mkdir -p "$BACKUP_DIR"

echo "备份目录: $BACKUP_DIR"

# 1. 备份菜单相关的视图文件
echo "1. 备份菜单视图文件..."
cp -r art-design-pro-main/src/views/system/menu "$BACKUP_DIR/views-menu"
cp -r art-design-pro-main/src/views/system/nested "$BACKUP_DIR/views-nested"

# 2. 备份菜单相关的API文件
echo "2. 备份菜单API文件..."
mkdir -p "$BACKUP_DIR/api"
cp art-design-pro-main/src/api/menuApi.ts "$BACKUP_DIR/api/"

# 3. 备份菜单相关的类型定义
echo "3. 备份菜单类型定义..."
mkdir -p "$BACKUP_DIR/types"
cp art-design-pro-main/src/types/system/menu.ts "$BACKUP_DIR/types/"

# 4. 备份菜单状态管理
echo "4. 备份菜单状态管理..."
mkdir -p "$BACKUP_DIR/store"
cp art-design-pro-main/src/store/modules/menu.ts "$BACKUP_DIR/store/"

# 5. 备份菜单路由工具
echo "5. 备份菜单路由工具..."
mkdir -p "$BACKUP_DIR/router-utils"
cp art-design-pro-main/src/router/utils/menuToRouter.ts "$BACKUP_DIR/router-utils/"

# 6. 备份菜单相关的布局组件
echo "6. 备份菜单布局组件..."
mkdir -p "$BACKUP_DIR/components"
cp -r art-design-pro-main/src/components/core/layouts/art-menus "$BACKUP_DIR/components/"

# 7. 备份设置面板中的菜单相关组件
echo "7. 备份设置面板组件..."
cp -r art-design-pro-main/src/components/core/layouts/art-settings-panel "$BACKUP_DIR/components/"

# 8. 备份用户状态管理（包含菜单相关状态）
echo "8. 备份用户状态管理..."
cp art-design-pro-main/src/store/modules/user.ts "$BACKUP_DIR/store/"
cp art-design-pro-main/src/store/modules/setting.ts "$BACKUP_DIR/store/"

# 9. 备份store类型定义
echo "9. 备份store类型定义..."
cp art-design-pro-main/src/types/store/system.ts "$BACKUP_DIR/types/"

# 10. 创建备份清单文件
echo "10. 创建备份清单..."
cat > "$BACKUP_DIR/BACKUP_MANIFEST.md" << EOF
# Art Design Pro 菜单模块备份清单

## 备份信息
- 备份时间: $(date '+%Y-%m-%d %H:%M:%S')
- 备份原因: 准备从RuoYi框架迁移菜单模块，避免代码冲突
- 备份范围: 所有与菜单管理相关的现有代码

## 备份文件清单

### 1. 视图组件 (views)
- \`views-menu/\` - 菜单管理页面组件
- \`views-nested/\` - 嵌套菜单示例组件

### 2. API接口 (api)
- \`api/menuApi.ts\` - 菜单相关API接口

### 3. 类型定义 (types)
- \`types/menu.ts\` - 菜单相关TypeScript类型定义
- \`types/system.ts\` - 系统相关类型定义

### 4. 状态管理 (store)
- \`store/menu.ts\` - 菜单状态管理store
- \`store/user.ts\` - 用户状态管理store（包含菜单相关状态）
- \`store/setting.ts\` - 设置状态管理store（包含菜单主题等）

### 5. 路由工具 (router-utils)
- \`router-utils/menuToRouter.ts\` - 菜单转路由的工具函数

### 6. 布局组件 (components)
- \`components/art-menus/\` - 菜单相关的布局组件
- \`components/art-settings-panel/\` - 设置面板组件

## 注意事项
1. 这些文件是art-design-pro-main框架原有的菜单系统
2. 在迁移RuoYi菜单模块时，请谨慎处理，避免覆盖重要功能
3. 建议在迁移完成后，对比新旧代码，确保功能完整性
4. 如需恢复，可以从此备份目录恢复相关文件

## 推荐迁移策略
1. 优先保留art-design-pro-main的UI组件和布局系统
2. 迁移RuoYi的业务逻辑和数据结构
3. 整合两套系统的优点，形成统一的菜单管理方案

EOF

echo "备份完成！"
echo "备份位置: $BACKUP_DIR"
echo "备份清单: $BACKUP_DIR/BACKUP_MANIFEST.md"